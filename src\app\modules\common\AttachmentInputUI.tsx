// AttachmentInputUI.tsx
import React, { ChangeEvent } from "react";
import { ImAttachment } from "react-icons/im";
import { IoMdClose } from "react-icons/io";
import { GoPlus } from "react-icons/go";
import { Loader } from "../../component";

export interface UploadedAttachment {
  id: string;
  url: string;
  type: string;
  name?: string; // File name
  previewUrl?: string; // Temporary local URL (for preview before upload)
  isUploading?: boolean;
}

interface UIProps {
  attachments: UploadedAttachment[];
  onUploadFile: (event: ChangeEvent<HTMLInputElement>) => void;
  onRemoveAttachment: (attachment: UploadedAttachment, idx: number) => void;
  id?: string;
  disabled?: boolean;
  acceptedFileTypes?: string; // New prop for dynamic file types
}

const AttachmentInputUI: React.FC<UIProps> = ({
  attachments,
  onUploadFile,
  onRemoveAttachment,
  id,
  disabled = false,
  acceptedFileTypes = "image/*", // Default to image/* for backward compatibility
}) => {
  console.log('acceptedFileTypes: ', acceptedFileTypes);
  return (
    <div className="attachment-container">
      {!disabled && attachments.length === 0 && (
        <label htmlFor={id} className="add-attachment-button">
          <ImAttachment />
          <span className="add-attachment-text">Add Attachment</span>
        </label>
      )}

      {!disabled && (
        <input
          id={id}
          type="file"
          onChange={onUploadFile}
          multiple
          className="file-input"
          style={{ display: "none" }}
          accept={acceptedFileTypes}
        />
      )}

      <div className="attachment-preview-container">
        {attachments.map((attachment, idx) => (
          <div key={idx} className="attachment-wrapper">
            <div className="file-details attachment-box">
              {attachment.type?.toLowerCase().startsWith('image') ? (
                <img
                  src={attachment.url || attachment.previewUrl}
                  alt={attachment.name}
                  className="inspection-attachment-preview"
                />
              ) : attachment.type?.toLowerCase().startsWith('video') ? (
                <video
                  src={attachment.url || attachment.previewUrl}
                  className="inspection-attachment-preview"
                  controls
                />
              ) : attachment.type?.toLowerCase().startsWith('audio') ? (
                <audio
                  src={attachment.url || attachment.previewUrl}
                  className="inspection-attachment-preview"
                  controls
                />
              ) : (
                <div className="inspection-attachment-preview file-placeholder">
                  <span>{attachment.name || 'File'}</span>
                </div>
              )}
              {attachment.isUploading && (
                <div className="loading-indicator">
                  <Loader />
                </div>
              )}
            </div>
            {!disabled && (
              <button
                type="button"
                className="remove-attachment-button"
                onClick={() => onRemoveAttachment(attachment, idx)}
                style={{
                  pointerEvents: attachment.isUploading ? "none" : "auto",
                }}
              >
                <IoMdClose style={{ height: 11, width: 11 }} />
              </button>
            )}
          </div>
        ))}

        {!disabled && attachments.length > 0 && (
          <label htmlFor={id} className="attachment-box add-more">
            <GoPlus style={{ width: 40, height: 40 }} />
          </label>
        )}
      </div>
    </div>
  );
};

export default AttachmentInputUI;
