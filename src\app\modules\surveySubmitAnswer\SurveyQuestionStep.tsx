import React from 'react';
import { SurveyQuestionResponseDto } from '../../apis/type';
import QuestionR<PERSON>ponseViewer from '../survey/components/addQuestions/QuestionResponseViewer';
import { ISurveyQuestionListItem } from '../../apis/type';

interface Props {
  question: SurveyQuestionResponseDto;
  questionNumber: number;
  answer: any;
  onAnswerChange: (answer: any) => void;
}

interface SurveyAnswer {
  answers: string[];
  comment: string;
  attachments: Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
  }>;
  branchingAnswer?: {
    answers: string[];
    comment: string;
    attachments: Array<{
      attachmentId: string;
      attachmentUrl: string;
      attachmentType: string;
    }>;
  };
}

const SurveyQuestionStep: React.FC<Props> = ({ 
  question, 
  questionNumber, 
  answer, 
  onAnswerChange 
}) => {
  // Convert SurveyQuestionResponseDto to ISurveyQuestionListItem format
  const convertedQuestion: ISurveyQuestionListItem = {
    id: question.id,
    surveyId: question.surveyId,
    questionText: question.questionText,
    responseType: question.responseType,
    options: question.options?.map((option, index) => ({
      optionText: option,
      optionValue: option,
      isTicketRequired: false,
    })) || [],
    startScale: question.startScale,
    endScale: question.endScale,
    ratting: question.ratting,
    rattingIcon: question.rattingIcon,
    startNetPromoterScore: question.startNetPromoterScore,
    endNetPromoterScore: question.endNetPromoterScore,
    isRequired: question.isRequired,
    allowAttachment: question.allowAttachment,
    attachmentType: question.attachmentType,
    allowComment: question.allowComment,
    comment: '',
    autoTicketGeneration: question.autoTicketGeneration,
    allowBranching: question.allowBranching,
    questionOrder: question.questionOrder,
    branchingQuestion: question.branchingQuestion ? {
      id: question.branchingQuestion.id,
      surveyId: question.branchingQuestion.surveyId,
      questionText: question.branchingQuestion.questionText,
      responseType: question.branchingQuestion.responseType,
      options: question.branchingQuestion.options?.map((option, index) => ({
        optionText: option,
        optionValue: option,
        isTicketRequired: false,
      })) || [],
      startScale: question.branchingQuestion.startScale,
      endScale: question.branchingQuestion.endScale,
      ratting: question.branchingQuestion.ratting,
      rattingIcon: question.branchingQuestion.rattingIcon,
      startNetPromoterScore: question.branchingQuestion.startNetPromoterScore,
      endNetPromoterScore: question.branchingQuestion.endNetPromoterScore,
      isRequired: question.branchingQuestion.isRequired,
      allowAttachment: question.branchingQuestion.allowAttachment,
      attachmentType: question.branchingQuestion.attachmentType || [],
      allowComment: question.branchingQuestion.allowComment || false,
      comment: '',
      autoTicketGeneration: question.branchingQuestion.autoTicketGeneration || false,
      allowBranching: question.branchingQuestion.allowBranching || false,
      questionOrder: question.branchingQuestion.questionOrder || 0,
      branchingQuestion: null,
    } : null,
  };

  // Handle answer changes from QuestionResponseViewer
  const handleAnswerUpdate = (updatedAnswer: any) => {
    // The QuestionResponseViewer will provide the answer in the format we need
    onAnswerChange(updatedAnswer);
  };

  return (
    <div className="survey-question-step">
      <QuestionResponseViewer
        question={convertedQuestion}
        questionNumber={questionNumber}
        readOnly={false}
        onAnswerChange={handleAnswerUpdate}
        initialAnswer={answer}
      />
    </div>
  );
};

export default SurveyQuestionStep;
