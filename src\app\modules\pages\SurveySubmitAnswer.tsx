import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useGetSurveyFullDetailsMutation } from '../../apis/survaysAPI';
import { ISurveyFullDetails } from '../../apis/type';
import { Loader } from '../../component';
import SwalMessage from '../common/SwalMessage';
import SurveyAnswerContainer from '../surveySubmitAnswer/SurveyAnswerContainer';

const SurveySubmitAnswer = () => {
  const [searchParams] = useSearchParams();
  const surveyId = searchParams.get('surveyId');

  const [getSurveyFullDetails, { isLoading }] = useGetSurveyFullDetailsMutation();
  const [surveyDetails, setSurveyDetails] = useState<ISurveyFullDetails | null>(null);

  useEffect(() => {
    if (surveyId) {
      getSurveyFullDetails({ surveyId })
        .unwrap()
        .then((res) => {
          if (res?.data) {
            setSurveyDetails(res.data);
          }
        })
        .catch(() => {
          SwalMessage(null, "Failed to load survey details", "Ok", "error", false);
        });
    }
  }, [surveyId, getSurveyFullDetails]);

  if (isLoading) {
    return <Loader />;
  }

  if (!surveyId) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
        <div className="text-center">
          <h4>Survey ID is required</h4>
          <p>Please provide a valid survey ID in the URL parameters.</p>
        </div>
      </div>
    );
  }

  if (!surveyDetails) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '50vh' }}>
        <div className="text-center">
          <h4>Survey not found</h4>
          <p>The requested survey could not be found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="survey-submit-answer-page">
      <SurveyAnswerContainer surveyDetails={surveyDetails} />
    </div>
  );
};

export default SurveySubmitAnswer;