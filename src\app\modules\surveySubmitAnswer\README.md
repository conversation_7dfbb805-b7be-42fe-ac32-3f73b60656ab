# Survey Submit Answer Module

This module implements the survey submission functionality where users can answer survey questions one by one.

## Features

- **One Question at a Time**: Shows one question per screen with navigation
- **Progress Tracking**: Visual progress bar and question counter (1/10)
- **Answer Persistence**: Automatically saves answers when navigating between questions
- **State Management**: Maintains local state for answers and surveyResponseId
- **Welcome Screen**: Shows survey welcome message and image before starting
- **Responsive Design**: Works on desktop and mobile devices

## Components

### SurveySubmitAnswer (Main Page)
- Entry point component at `src/app/modules/pages/SurveySubmitAnswer.tsx`
- Fetches survey details using `useGetSurveyFullDetailsMutation`
- Handles loading states and error cases
- Expects `surveyId` as URL parameter

### SurveyAnswerContainer
- Main container component that manages the survey flow
- Handles welcome screen display
- Manages current question index and navigation
- Integrates with `useSurveyAnswerState` hook for state management

### SurveyQuestionStep
- Renders individual questions using the existing `QuestionResponseViewer`
- Converts `SurveyQuestionResponseDto` to `ISurveyQuestionListItem` format
- <PERSON><PERSON> answer changes and passes them to parent

### SurveyNavigation
- Provides Back/Next navigation buttons
- Shows question counter (current/total)
- Changes "Next" to "Submit" on the last question
- Handles loading states during submission

### useSurveyAnswerState Hook
- Manages survey answer state and API calls
- Stores answers with surveyResponseId for editing
- Handles `useSubmitSurveyResponseMutation` API calls
- Provides methods for updating answers and submitting

## API Integration

### Survey Details
- Uses `useGetSurveyFullDetailsMutation` to fetch survey details
- Expects `{ surveyId: string }` payload

### Answer Submission
- Uses `useSubmitSurveyResponseMutation` for each question
- Payload includes:
  - `surveyId`: Survey identifier
  - `surveyQuestionId`: Question identifier (from `surveyQuestionResponseDtos.id`)
  - `answers`: Array of selected answers
  - `comment`: User comment if allowed
  - `attachmentDtoList`: File attachments if allowed
  - `branchingQuestion`: Branching question data if applicable
  - `surveyResponseId`: For editing existing answers (null for first submission)

## State Management

The hook maintains a state structure:
```typescript
{
  [questionId: string]: {
    answer: SurveyAnswer;
    surveyResponseId?: string;
  }
}
```

## Navigation Flow

1. **Welcome Screen**: Shows survey name, image, and welcome message
2. **Question Flow**: One question at a time with Back/Next navigation
3. **Auto-Save**: Calls API on each navigation to save current answer
4. **Final Submit**: Console logs "Survey Submitted" as requested

## Usage

Navigate to the page with surveyId parameter:
```
/survey-submit-answer?surveyId=your-survey-id
```

## File Structure

```
src/app/modules/surveySubmitAnswer/
├── SurveyAnswerContainer.tsx     # Main container
├── SurveyQuestionStep.tsx        # Individual question renderer
├── SurveyNavigation.tsx          # Navigation component
├── SurveySubmitAnswer.css        # Styling
├── hooks/
│   └── useSurveyAnswerState.ts   # State management hook
└── README.md                     # This file
```

## Dependencies

- Existing `QuestionResponseViewer` component for question rendering
- Survey APIs from `src/app/apis/survaysAPI.ts`
- Bootstrap for styling
- React Router for URL parameters
