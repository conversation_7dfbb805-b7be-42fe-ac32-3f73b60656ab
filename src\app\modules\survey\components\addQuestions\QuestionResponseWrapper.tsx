import React from "react";
import FormLabel from "../../../../component/form/FormLabel";
import FormInput from "../../../../component/form/FormInput";
import QuestionAttchmentInput from "./QuestionAttchmentInput";

interface QuestionResponseWrapperProps {
  children: React.ReactNode;
  questionText: string;
  isRequired?: boolean;
  allowAttachment?: boolean;
  allowedAttachmentType?: string[];
  attachments?: Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
    attachmentName?: string;
  }>;
  onAttachmentsChange?: (attachments: Array<{
    attachmentUrl: string;
    attachmentType: string;
    attachmentId: string;
  }>) => void;
  allowComment?: boolean;
  allowBranching?: boolean;
  branchingQuestionText?: string;
  allowbranchingAttchement?: boolean;
  branchingAttchementType?: string[];
  branchingAttachments?: Array<{
    attachmentId: string;
    attachmentUrl: string;
    attachmentType: string;
    attachmentName?: string;
  }>;
  onBranchingAttachmentsChange?: (attachments: Array<{
    attachmentUrl: string;
    attachmentType: string;
    attachmentId: string;
  }>) => void;
  questionNumber?: number;
  readOnly?: boolean;
}

const QuestionResponseWrapper: React.FC<QuestionResponseWrapperProps> = ({
  questionText,
  isRequired = false,
  allowAttachment = false,
  allowedAttachmentType = [],
  attachments = [],
  onAttachmentsChange,
  allowComment = false,
  allowBranching = false,
  branchingQuestionText = "",
  allowbranchingAttchement = false,
  branchingAttchementType = [],
  branchingAttachments = [],
  onBranchingAttachmentsChange,
  children,
  questionNumber,
  readOnly = false,
}) => {
  // Debug logging
  console.log('QuestionResponseWrapper - allowedAttachmentType:', allowedAttachmentType);
  console.log('QuestionResponseWrapper - allowAttachment:', allowAttachment);

  return (
    <div className="question-response-wrapper">
      <div className="d-flex align-items-start">
        <FormLabel required={isRequired}>{`${
          questionNumber ? questionNumber + ")" : ""
        } ${questionText}`}</FormLabel>
      </div>
      <div className="">{children}</div>

      {allowAttachment && (
        <div className="mt-5">
          <FormLabel required={false}>Attachment</FormLabel>
          <QuestionAttchmentInput
            attachments={attachments}
            allowedAttachmentTypes={allowedAttachmentType}
            onAttachmentsChange={onAttachmentsChange}
            disabled={readOnly}
            id="question-attachment-input"
          />
        </div>
      )}
      
      {allowComment && (
        <div className="mt-5">
          <FormLabel required={false}>Add Comment</FormLabel>
          <FormInput
            name="comment"
            as="textarea"
            readOnly
            placeholder="Add your comment..."
          />
        </div>
      )}
      {allowBranching && branchingQuestionText && (
        <div className="">
          <hr className="opacity-5" />
          <FormLabel required={false}>{branchingQuestionText}</FormLabel>
          {/* //TODO : Make it  */}
          <input
            className="form-control"
            // value={branchingQuestionText}
            name="branchingQuestionTextAns"
            // readOnly
            // rows={2}
          />
          {allowbranchingAttchement && (
            <div className="mt-5">
              <FormLabel required={false}>Branching Attachment</FormLabel>
              <QuestionAttchmentInput
                attachments={branchingAttachments}
                allowedAttachmentTypes={branchingAttchementType}
                onAttachmentsChange={onBranchingAttachmentsChange}
                disabled={readOnly}
                id="branching-attachment-input"
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default QuestionResponseWrapper;
