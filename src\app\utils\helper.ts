import moment from "moment";
import SwalMessage from "../modules/common/SwalMessage";
import { swalMessages } from "./CommonUtils";
import { IResponse } from "../apis/type";
import { pointOption } from "../modules/inspection/constant";

export const defaultSortingColumn = [{
  sortOrder: 0,
  columnName: "",
}];

export const formatLastMessageTime = (timestamp: number | string) => {
  if (!timestamp) return;

  const now = moment();
  const messageTime = moment(timestamp);
  const time = messageTime.format("h:mm A");
  if (messageTime.isSame(now, "day")) {
    return time; // Same day: Time only
  } else if (messageTime.isSame(now, "week")) {
    return `${messageTime.format("ddd, h:mm A")}`; // Same week: Day and time
  } else {
    return `${messageTime.format("MMM D, YYYY")} | ${time}`; // Older: Full date
  }
};

/**
 * @description Checks if an object has any truthy value.
 * @param obj - The object to check.
 * @returns `true` if the object contains at least one truthy value, otherwise `false`.
 */
export function hasTruthyValue(obj: object): boolean {
  return Object.values(obj).some((value) => !!value);
}

//* Convert came case into title case
export function camelCaseToTitleCase(camelCaseStr: string): string {
  return camelCaseStr
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (char) => char.toUpperCase())
    .trim();
}

export function snakeToTitleCase(snakeCaseString: string): string {
  return snakeCaseString
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

export function extractTextFromHTML(html: string): string {
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = html; // Set the HTML content
  return tempDiv.textContent || ""; // Extract and return the text content
}

interface ConfirmationOptions {
  callback: () => void; // Required callback
  title?: string; // Optional title
  newTitle?: string; // Optional completely new title (overrides title and excludes endMessage)
  confirmButtonText?: string; // Optional button text (default: "Delete")
  icon?: string; // Optional icon type (default: info)
  showCancelButton?: boolean; // Optional show cancel button (default: true)
}

export const isConfirmation = async ({
  callback,
  title,
  newTitle,
  confirmButtonText = "Delete",
  icon = swalMessages.icon.info,
  showCancelButton = true,
}: ConfirmationOptions) => {
  const finalTitle = newTitle || swalMessages.title.commonTitle; // Use newTitle if provided, otherwise use commonTitle
  const text = newTitle ? "" : `${title || ""} ${swalMessages?.endMessage}`; // Skip endMessage if newTitle is used

  const confirm = await SwalMessage(
    finalTitle,
    text.trim(), // Trim to handle cases where text might be empty
    confirmButtonText,
    icon,
    showCancelButton
  );

  if (confirm) {
    callback();
  }
};

export const getFileType = (type: string): string => {
  switch (type) {
    // Image types
    case "image/png":
    case "image/jpeg":
    case "image/jpg":
    case "image/gif":
    case "image/bmp":
    case "image/webp":
    case "image/tiff":
    case "image/svg+xml":
      return "IMAGE";

    // Audio types
    case "audio/mp3":
    case "audio/mpeg":
    case "audio/wav":
    case "audio/ogg":
    case "audio/flac":
    case "audio/aac":
    case "audio/x-wav":
    case "audio/x-m4a":
    case "audio/webm":
      return "AUDIO";

    // Video types
    case "video/mp4":
    case "video/mkv":
    case "video/webm":
    case "video/avi":
    case "video/mov":
    case "video/wmv":
    case "video/flv":
    case "video/3gp":
    case "video/mpeg":
    case "video/ogg":
    case "video/quicktime":
      return "VIDEO";

    // Unsupported types
    default:
      console.error(`Unsupported file type: ${type}`);
      return ""; // Or handle unsupported types as needed
  }
};

interface ProcessApiResponseOptions<T> {
  res: IResponse<T>;
  onSuccess?: (data: IResponse<T>) => void;
  onFailure?: (error: string) => void;
  failureMessage?: string;
  successMessage?: string;
}

/**
 * Handles a standardized API response in a type-safe and flexible way.
 *
 * @template T - The type of the response data.
 * @param options - Options object containing:
 *   - res: API response object.
 *   - onSuccess: Optional success callback.
 *   - onFailure: Optional failure callback.
 *   - failureMessage: Optional custom failure message for UI alerts.
 *
 * @example
 * processApiResponse({
 *   res: response,
 *   onSuccess: (data) => console.log('Success:', data),
 *   onFailure: (error) => console.error('Error:', error),
 *   failureMessage: 'Custom error message'
 * });
 */
export function processApiResponse<T>({
  res,
  onSuccess,
  onFailure,
  failureMessage,
  successMessage,
}: ProcessApiResponseOptions<T>): void {
  if (res.success) {
    onSuccess?.(res);
  } else {
    SwalMessage(null, failureMessage || res?.errormsg, "Ok", "error", false);
    onFailure?.(res.errormsg);
  }
}

/**
 * @dateStr : 2025-04-22 10:46:45.095";
 * return :  Apr 22, 2025, 10:46 AM
 */
export function formatCustomDate(dateStr: string) {
  const date = new Date(dateStr.replace(" ", "T"));
  const options: Intl.DateTimeFormatOptions = {
    month: "short",
    day: "numeric",
    year: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  };
  return date.toLocaleString("en-US", options);
}

/**
 * Retrieves a point option object from a predefined list based on the provided value.
 *
 * @param value - The value to search for in the point options.
 * @returns The matching point option object if found, otherwise `undefined`.
 *
 * @remarks
 * Use this function when you need to find a specific point option by its value
 * from a predefined list of options.
 */
export const getPointOptionByValue = (value: string) => {
  return pointOption.find((option) => option.value === value);
};

/**
 * Converts a snake_case string to Title Case.
 * Handles both uppercase and lowercase input.
 * Examples:
 *   MEMBER_BASE => Member Base
 *   memeber_Base => Member Base
 * @param snakeCaseString - The snake_case string to convert
 * @returns The string in Title Case
 */
export function snakeCaseToTitleCase(snakeCaseString: string): string {
  if (!snakeCaseString) return "";
  return snakeCaseString
    .toLowerCase()
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}
